2025-09-10 01:34:00,252 - INFO - SPY.py:2359 - Logging configured for Option Trader v1.
2025-09-10 01:34:00,252 - INFO - SPY.py:2360 - Log file: C:\Users\<USER>\Desktop\OHLC + Volume + TNX + VIX3MVIX ratio\rl_logs_yfinance_options_v1\spy_option_training_v1.log
2025-09-10 01:34:00,253 - INFO - SPY.py:2361 - Tickers to fetch: ['SPY', '^VIX', '^VIX3M', '^IRX', '^TNX']
2025-09-10 01:34:00,253 - INFO - SPY.py:2369 - YFinance session management disabled - letting yfinance handle its own sessions with curl_cffi.
2025-09-10 01:34:00,256 - INFO - SPY.py:5971 - --- Option Trader v3.1 ---
2025-09-10 01:34:00,256 - INFO - SPY.py:5981 - Signal generation mode...
2025-09-10 01:34:03,475 - INFO - SPY.py:2508 - Using min_periods_override=50 for SPY
2025-09-10 01:34:03,478 - INFO - SPY.py:2651 - SPY expanded feature set: Keeping OHLCV columns ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY', 'volume_SPY']
2025-09-10 01:34:03,479 - INFO - SPY.py:2657 - Using expanded OHLCV feature set for SPY
2025-09-10 01:34:03,479 - INFO - SPY.py:2660 - SPY: Keeping OHLCV columns ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY', 'volume_SPY']
2025-09-10 01:34:03,482 - INFO - SPY.py:2688 - Processed SPY data shape: (63, 5). Columns: ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY', 'volume_SPY']
2025-09-10 01:34:03,482 - INFO - SPY.py:2689 - Data quality check: 0/315 (0.0%) zero values
2025-09-10 01:34:03,482 - INFO - SPY.py:734 - [INFO] PerformanceMonitor: Operation fetch_refactored_SPY_1757439240 SUCCESS in 3.23s [Ticker: SPY] [Operation: refactored_fetch]
2025-09-10 01:34:04,285 - INFO - SPY.py:2187 - Attempt 1/3 with timeout 20s
2025-09-10 01:34:04,975 - INFO - SPY.py:2508 - Using min_periods_override=50 for ^VIX
2025-09-10 01:34:04,975 - INFO - SPY.py:2532 - Processing ^VIX data with shape (63, 7)
2025-09-10 01:34:04,975 - INFO - SPY.py:2533 - Raw ^VIX columns: ['Open', 'High', 'Low', 'Close', 'Volume', 'Dividends', 'Stock Splits']
2025-09-10 01:34:04,976 - INFO - SPY.py:2535 - Raw ^VIX Close values - first: 20.510000228881836, last: 20.81999969482422
2025-09-10 01:34:04,978 - INFO - SPY.py:2455 - Applying VIX validation for VIX
2025-09-10 01:34:04,978 - INFO - SPY.py:2614 - Processed VIX close values - first: 20.510000228881836, last: 20.81999969482422
2025-09-10 01:34:04,979 - INFO - SPY.py:2647 - Market index ^VIX: Keeping only ['close_VIX']
2025-09-10 01:34:04,981 - INFO - SPY.py:2688 - Processed ^VIX data shape: (63, 1). Columns: ['close_VIX']
2025-09-10 01:34:04,981 - INFO - SPY.py:2689 - Data quality check: 0/63 (0.0%) zero values
2025-09-10 01:34:04,981 - INFO - SPY.py:1161 - VALIDATION: ^VIX last value: 20.81999969482422
2025-09-10 01:34:04,981 - INFO - SPY.py:734 - [INFO] PerformanceMonitor: Operation fetch_refactored_^VIX_1757439243 SUCCESS in 1.50s [Ticker: ^VIX] [Operation: refactored_fetch]
2025-09-10 01:34:05,784 - INFO - SPY.py:2187 - Attempt 1/3 with timeout 25s
2025-09-10 01:34:06,320 - INFO - SPY.py:2508 - Using min_periods_override=50 for ^VIX3M
2025-09-10 01:34:06,324 - INFO - SPY.py:2647 - Market index ^VIX3M: Keeping only ['close_VIX3M']
2025-09-10 01:34:06,325 - INFO - SPY.py:2688 - Processed ^VIX3M data shape: (63, 1). Columns: ['close_VIX3M']
2025-09-10 01:34:06,326 - INFO - SPY.py:2689 - Data quality check: 0/63 (0.0%) zero values
2025-09-10 01:34:06,326 - INFO - SPY.py:1161 - VALIDATION: ^VIX3M last value: 22.6200008392334
2025-09-10 01:34:06,326 - INFO - SPY.py:734 - [INFO] PerformanceMonitor: Operation fetch_refactored_^VIX3M_1757439244 SUCCESS in 1.34s [Ticker: ^VIX3M] [Operation: refactored_fetch]
2025-09-10 01:34:07,128 - INFO - SPY.py:2187 - Attempt 1/3 with timeout 20s
2025-09-10 01:34:07,638 - INFO - SPY.py:2508 - Using min_periods_override=50 for ^IRX
2025-09-10 01:34:07,638 - INFO - SPY.py:2532 - Processing ^IRX data with shape (63, 7)
2025-09-10 01:34:07,639 - INFO - SPY.py:2533 - Raw ^IRX columns: ['Open', 'High', 'Low', 'Close', 'Volume', 'Dividends', 'Stock Splits']
2025-09-10 01:34:07,639 - INFO - SPY.py:2535 - Raw ^IRX Close values - first: 4.188000202178955, last: 4.239999771118164
2025-09-10 01:34:07,641 - INFO - SPY.py:2472 - Applying standardized Treasury rate validation for IRX (3-month Treasury yield)
2025-09-10 01:34:07,641 - INFO - SPY.py:2473 - ASSUMPTION: Input data is in percentage format, will convert to decimal format
2025-09-10 01:34:07,642 - INFO - SPY.py:2482 - IRX (3-month Treasury yield) converted from percentage to decimal format
2025-09-10 01:34:07,643 - INFO - SPY.py:2485 - IRX (3-month Treasury yield) after conversion to decimal: min=0.041500, max=0.042850, median=0.042080
2025-09-10 01:34:07,644 - INFO - SPY.py:2614 - Processed IRX (3-month Treasury yield) close values - first: 0.04188000202178955, last: 0.04239999771118164
2025-09-10 01:34:07,644 - INFO - SPY.py:2621 - VALIDATION: Final IRX (3-month Treasury yield) rate (decimal format): 0.042400 (4.2400%)
2025-09-10 01:34:07,645 - INFO - SPY.py:2647 - Market index ^IRX: Keeping only ['close_IRX']
2025-09-10 01:34:07,646 - INFO - SPY.py:2688 - Processed ^IRX data shape: (63, 1). Columns: ['close_IRX']
2025-09-10 01:34:07,647 - INFO - SPY.py:2689 - Data quality check: 0/63 (0.0%) zero values
2025-09-10 01:34:07,647 - INFO - SPY.py:1161 - VALIDATION: ^IRX last value: 0.04239999771118164
2025-09-10 01:34:07,647 - INFO - SPY.py:734 - [INFO] PerformanceMonitor: Operation fetch_refactored_^IRX_1757439246 SUCCESS in 1.32s [Ticker: ^IRX] [Operation: refactored_fetch]
2025-09-10 01:34:08,449 - INFO - SPY.py:2187 - Attempt 1/3 with timeout 20s
2025-09-10 01:34:09,319 - INFO - SPY.py:2508 - Using min_periods_override=50 for ^TNX
2025-09-10 01:34:09,322 - INFO - SPY.py:2472 - Applying standardized Treasury rate validation for TNX (10-year Treasury yield)
2025-09-10 01:34:09,323 - INFO - SPY.py:2473 - ASSUMPTION: Input data is in percentage format, will convert to decimal format
2025-09-10 01:34:09,324 - INFO - SPY.py:2482 - TNX (10-year Treasury yield) converted from percentage to decimal format
2025-09-10 01:34:09,324 - INFO - SPY.py:2485 - TNX (10-year Treasury yield) after conversion to decimal: min=0.039850, max=0.045960, median=0.043650
2025-09-10 01:34:09,325 - INFO - SPY.py:2614 - Processed TNX (10-year Treasury yield) close values - first: 0.04306000232696533, last: 0.04423999786376953
2025-09-10 01:34:09,325 - INFO - SPY.py:2621 - VALIDATION: Final TNX (10-year Treasury yield) rate (decimal format): 0.044240 (4.4240%)
2025-09-10 01:34:09,326 - INFO - SPY.py:2647 - Market index ^TNX: Keeping only ['close_TNX']
2025-09-10 01:34:09,328 - INFO - SPY.py:2688 - Processed ^TNX data shape: (63, 1). Columns: ['close_TNX']
2025-09-10 01:34:09,329 - INFO - SPY.py:2689 - Data quality check: 0/63 (0.0%) zero values
2025-09-10 01:34:09,329 - INFO - SPY.py:1161 - VALIDATION: ^TNX last value: 0.04423999786376953
2025-09-10 01:34:09,329 - INFO - SPY.py:734 - [INFO] PerformanceMonitor: Operation fetch_refactored_^TNX_1757439247 SUCCESS in 1.68s [Ticker: ^TNX] [Operation: refactored_fetch]
2025-09-10 01:34:09,335 - INFO - SPY.py:2884 - Created master index with 63 unique dates from 2025-03-17 to 2025-06-13
2025-09-10 01:34:09,336 - INFO - SPY.py:2889 - Processing ticker SPY for reindexing: shape=(63, 5), columns=['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY', 'volume_SPY']
2025-09-10 01:34:09,337 - INFO - SPY.py:2895 - About to reindex SPY with master_index length 63
2025-09-10 01:34:09,337 - INFO - SPY.py:2749 - Index overlap for SPY: 100.0% (63/63 dates)
2025-09-10 01:34:09,342 - INFO - SPY.py:2812 - Successfully reindexed SPY with validation passed
2025-09-10 01:34:09,342 - INFO - SPY.py:2899 - Successfully reindexed SPY to master index. New shape: (63, 5)
2025-09-10 01:34:09,342 - INFO - SPY.py:2889 - Processing ticker ^VIX for reindexing: shape=(63, 1), columns=['close_VIX']
2025-09-10 01:34:09,344 - INFO - SPY.py:2895 - About to reindex ^VIX with master_index length 63
2025-09-10 01:34:09,344 - INFO - SPY.py:2749 - Index overlap for ^VIX: 100.0% (63/63 dates)
2025-09-10 01:34:09,346 - INFO - SPY.py:2812 - Successfully reindexed ^VIX with validation passed
2025-09-10 01:34:09,346 - INFO - SPY.py:2899 - Successfully reindexed ^VIX to master index. New shape: (63, 1)
2025-09-10 01:34:09,346 - INFO - SPY.py:2889 - Processing ticker ^VIX3M for reindexing: shape=(63, 1), columns=['close_VIX3M']
2025-09-10 01:34:09,347 - INFO - SPY.py:2895 - About to reindex ^VIX3M with master_index length 63
2025-09-10 01:34:09,347 - INFO - SPY.py:2749 - Index overlap for ^VIX3M: 100.0% (63/63 dates)
2025-09-10 01:34:09,348 - INFO - SPY.py:2812 - Successfully reindexed ^VIX3M with validation passed
2025-09-10 01:34:09,348 - INFO - SPY.py:2899 - Successfully reindexed ^VIX3M to master index. New shape: (63, 1)
2025-09-10 01:34:09,349 - INFO - SPY.py:2889 - Processing ticker ^IRX for reindexing: shape=(63, 1), columns=['close_IRX']
2025-09-10 01:34:09,350 - INFO - SPY.py:2895 - About to reindex ^IRX with master_index length 63
2025-09-10 01:34:09,351 - INFO - SPY.py:2749 - Index overlap for ^IRX: 100.0% (63/63 dates)
2025-09-10 01:34:09,352 - INFO - SPY.py:2812 - Successfully reindexed ^IRX with validation passed
2025-09-10 01:34:09,352 - INFO - SPY.py:2899 - Successfully reindexed ^IRX to master index. New shape: (63, 1)
2025-09-10 01:34:09,353 - INFO - SPY.py:2889 - Processing ticker ^TNX for reindexing: shape=(63, 1), columns=['close_TNX']
2025-09-10 01:34:09,353 - INFO - SPY.py:2895 - About to reindex ^TNX with master_index length 63
2025-09-10 01:34:09,354 - INFO - SPY.py:2749 - Index overlap for ^TNX: 100.0% (63/63 dates)
2025-09-10 01:34:09,355 - INFO - SPY.py:2812 - Successfully reindexed ^TNX with validation passed
2025-09-10 01:34:09,355 - INFO - SPY.py:2899 - Successfully reindexed ^TNX to master index. New shape: (63, 1)
2025-09-10 01:34:09,355 - INFO - SPY.py:2917 - Starting combine features with SPY shape: (63, 5)
2025-09-10 01:34:09,355 - INFO - SPY.py:2918 - Available tickers in reindexed_data_dict: ['SPY', '^VIX', '^VIX3M', '^IRX', '^TNX']
2025-09-10 01:34:09,356 - INFO - SPY.py:2934 - Merging ^VIX (Shape: (63, 1), Columns: ['close_VIX'])
2025-09-10 01:34:09,356 - INFO - SPY.py:2944 - Columns for ^VIX already appear to be renamed. Using as-is.
2025-09-10 01:34:09,357 - INFO - SPY.py:2980 - SPY index range: 2025-03-17 to 2025-06-13, count: 63
2025-09-10 01:34:09,357 - INFO - SPY.py:2981 - ^VIX index range: 2025-03-17 to 2025-06-13, count: 63
2025-09-10 01:34:09,358 - INFO - SPY.py:2998 - ^VIX close_VIX sample after join (first 3): [20.510000228881836, 21.700000762939453, 19.899999618530273], last: 20.81999969482422
2025-09-10 01:34:09,359 - INFO - SPY.py:3016 - After joining ^VIX, combined_df columns: ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY', 'volume_SPY', 'close_VIX']
2025-09-10 01:34:09,359 - INFO - SPY.py:2934 - Merging ^VIX3M (Shape: (63, 1), Columns: ['close_VIX3M'])
2025-09-10 01:34:09,359 - INFO - SPY.py:2944 - Columns for ^VIX3M already appear to be renamed. Using as-is.
2025-09-10 01:34:09,360 - INFO - SPY.py:2980 - SPY index range: 2025-03-17 to 2025-06-13, count: 63
2025-09-10 01:34:09,361 - INFO - SPY.py:2981 - ^VIX3M index range: 2025-03-17 to 2025-06-13, count: 63
2025-09-10 01:34:09,362 - INFO - SPY.py:2998 - ^VIX3M close_VIX3M sample after join (first 3): [21.299999237060547, 21.969999313354492, 20.829999923706055], last: 22.6200008392334
2025-09-10 01:34:09,363 - INFO - SPY.py:3016 - After joining ^VIX3M, combined_df columns: ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY', 'volume_SPY', 'close_VIX', 'close_VIX3M']
2025-09-10 01:34:09,364 - INFO - SPY.py:2934 - Merging ^IRX (Shape: (63, 1), Columns: ['close_IRX'])
2025-09-10 01:34:09,365 - INFO - SPY.py:2944 - Columns for ^IRX already appear to be renamed. Using as-is.
2025-09-10 01:34:09,367 - INFO - SPY.py:2980 - SPY index range: 2025-03-17 to 2025-06-13, count: 63
2025-09-10 01:34:09,367 - INFO - SPY.py:2981 - ^IRX index range: 2025-03-17 to 2025-06-13, count: 63
2025-09-10 01:34:09,369 - INFO - SPY.py:2998 - ^IRX close_IRX sample after join (first 3): [0.04188000202178955, 0.04195000171661377, 0.04190000057220459], last: 0.04239999771118164
2025-09-10 01:34:09,369 - INFO - SPY.py:3016 - After joining ^IRX, combined_df columns: ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY', 'volume_SPY', 'close_VIX', 'close_VIX3M', 'close_IRX']
2025-09-10 01:34:09,369 - INFO - SPY.py:2934 - Merging ^TNX (Shape: (63, 1), Columns: ['close_TNX'])
2025-09-10 01:34:09,370 - INFO - SPY.py:2944 - Columns for ^TNX already appear to be renamed. Using as-is.
2025-09-10 01:34:09,371 - INFO - SPY.py:2980 - SPY index range: 2025-03-17 to 2025-06-13, count: 63
2025-09-10 01:34:09,371 - INFO - SPY.py:2981 - ^TNX index range: 2025-03-17 to 2025-06-13, count: 63
2025-09-10 01:34:09,372 - INFO - SPY.py:2998 - ^TNX close_TNX sample after join (first 3): [0.04306000232696533, 0.042810001373291016, 0.042560000419616696], last: 0.04423999786376953
2025-09-10 01:34:09,373 - INFO - SPY.py:3016 - After joining ^TNX, combined_df columns: ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY', 'volume_SPY', 'close_VIX', 'close_VIX3M', 'close_IRX', 'close_TNX']
2025-09-10 01:34:09,373 - INFO - SPY.py:3027 - Shape after merging all tickers: (63, 9)
2025-09-10 01:34:09,374 - INFO - SPY.py:3310 - Skipping VIX derived feature calculations - using reduced feature set
2025-09-10 01:34:09,374 - INFO - SPY.py:3314 - Skipping Treasury derived feature calculations - using reduced feature set
2025-09-10 01:34:09,374 - INFO - SPY.py:3091 - Applying normalization to volume_SPY feature...
2025-09-10 01:34:09,375 - INFO - SPY.py:3108 - Volume normalization applied:
2025-09-10 01:34:09,376 - INFO - SPY.py:3109 -   Original volume range: 37603400 to 256611400
2025-09-10 01:34:09,376 - INFO - SPY.py:3110 -   Log-transformed range: 17.4426 to 19.3631
2025-09-10 01:34:09,376 - INFO - SPY.py:3111 -   Normalized range: -1.6874 to 3.2291
2025-09-10 01:34:09,377 - INFO - SPY.py:3112 -   Normalized mean: 0.0000, std: 1.0000
2025-09-10 01:34:09,377 - INFO - SPY.py:3119 - Performing final validation and cleanup...
2025-09-10 01:34:09,378 - INFO - SPY.py:3141 - Skipping yield spread calculation - using individual TNX and IRX close prices for RL features
2025-09-10 01:34:09,378 - INFO - SPY.py:3144 - Calculating VIX term structure ratio (close_VIX3M / close_VIX) for RL features
2025-09-10 01:34:09,381 - INFO - SPY.py:3170 - VIX term ratio statistics: min=0.7850, max=1.1938, mean=1.0473
2025-09-10 01:34:09,383 - INFO - SPY.py:3192 - Created 7-feature set with shape: (63, 7)
2025-09-10 01:34:09,383 - INFO - SPY.py:3193 - 7-feature columns: ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY', 'volume_SPY', 'close_TNX', 'vix_term_ratio']
2025-09-10 01:34:09,383 - INFO - SPY.py:3198 - Stored full market data for option pricing with shape: (63, 9)
2025-09-10 01:34:09,383 - INFO - SPY.py:3199 - Full market data columns: ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY', 'volume_SPY', 'close_VIX', 'close_VIX3M', 'close_IRX', 'close_TNX']
2025-09-10 01:34:09,384 - INFO - SPY.py:3200 - IRX available in full market data for option pricing: True
2025-09-10 01:34:09,384 - INFO - SPY.py:3204 - Expected columns for 7-feature set: ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY', 'volume_SPY', 'close_TNX', 'vix_term_ratio']
2025-09-10 01:34:09,385 - INFO - SPY.py:3225 - SPY volume validation passed: min=-1.687353, max=3.229137, mean=0.000000
2025-09-10 01:34:09,386 - INFO - SPY.py:3245 - VIX term ratio validation passed: min=0.785015, max=1.193798, mean=1.047254
2025-09-10 01:34:09,386 - INFO - SPY.py:3266 - TNX validation passed: min=0.0398, max=0.0460, mean=0.0435
2025-09-10 01:34:09,387 - INFO - SPY.py:3281 - IRX validation passed in full market data: min=0.0415, max=0.0428, mean=0.0421
2025-09-10 01:34:09,387 - INFO - SPY.py:3332 - Skipping feature distribution validation - using expanded feature set with SPY OHLC + TNX (IRX excluded from RL features)
2025-09-10 01:34:09,387 - INFO - SPY.py:3293 - Final 7-feature set shape: (63, 7) (7 features: SPY OHLCV + TNX + VIX term ratio)
2025-09-10 01:34:09,387 - INFO - SPY.py:3294 - Final 7-feature columns: ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY', 'volume_SPY', 'close_TNX', 'vix_term_ratio']
2025-09-10 01:34:09,387 - INFO - SPY.py:3295 - IRX remains available in full market data for option pricing: True
2025-09-10 01:34:09,388 - INFO - SPY.py:6033 - N_MARKET_FEATURES: 7
2025-09-10 01:34:09,388 - ERROR - SPY.py:6062 - Model not found: C:\Users\<USER>\Desktop\OHLC + Volume + TNX + VIX3MVIX ratio\models_options_v1\ppo_spy_option_trader_v1.zip
2025-09-10 01:34:09,388 - INFO - SPY.py:9407 - [INFO] Main: Script execution completed
2025-09-10 01:34:09,388 - INFO - SPY.py:779 - [INFO] PerformanceMonitor: Performance Summary: 5/5 operations successful (100.0% success rate)
2025-09-10 01:34:09,389 - INFO - SPY.py:787 - [INFO] PerformanceMonitor: Average refactored_fetch time: 1.81s
